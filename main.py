#!/usr/bin/env python3
"""
OpenAI API Demo Script
This script demonstrates how to use the OpenAI API to get responses from an LLM.
"""

import os
from dotenv import load_dotenv
from openai import OpenAI

def main():
    """Main function to demonstrate OpenAI API usage."""

    # Load environment variables from .env file
    load_dotenv()

    # Get the OpenAI API key from environment variables
    api_key = os.getenv("OPENAI_API_KEY")

    if not api_key:
        print("❌ Error: OPENAI_API_KEY not found in environment variables.")
        print("Please make sure your .env file contains a valid OpenAI API key.")
        return

    if api_key == "sk-your-actual-openai-api-key-here":
        print("❌ Error: Please replace the placeholder API key with your actual OpenAI API key.")
        print("Get your API key from: https://platform.openai.com/api-keys")
        return

    try:
        # Initialize the OpenAI client
        client = OpenAI(api_key=api_key)

        print("🤖 OpenAI API Demo")
        print("=" * 50)

        # Get user input for the prompt
        user_prompt = input("Enter your question or prompt: ").strip()

        if not user_prompt:
            user_prompt = "Explain what artificial intelligence is in simple terms."
            print(f"Using default prompt: {user_prompt}")

        print("\n🔄 Sending request to OpenAI...")

        # Make a request to the OpenAI API
        response = client.chat.completions.create(
            model="gpt-4o-mini",  # You can change this to "gpt-4" if you have access
            messages=[
                {
                    "role": "system",
                    "content": "You are a helpful assistant that provides clear and concise answers."
                },
                {
                    "role": "user",
                    "content": user_prompt
                }
            ],
            max_tokens=500,
            temperature=0.7
        )

        # Extract and display the response
        ai_response = response.choices[0].message.content

        print("\n✅ Response from OpenAI:")
        print("-" * 50)
        print(ai_response)
        print("-" * 50)

        # Display some metadata about the response
        print(f"\n📊 Response Info:")
        print(f"Model used: {response.model}")
        print(f"Tokens used: {response.usage.total_tokens}")
        print(f"Prompt tokens: {response.usage.prompt_tokens}")
        print(f"Completion tokens: {response.usage.completion_tokens}")

    except Exception as e:
        print(f"❌ Error occurred: {str(e)}")
        print("Please check your API key and internet connection.")

if __name__ == "__main__":
    main()